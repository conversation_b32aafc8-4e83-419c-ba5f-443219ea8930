# 分类顺序配置系统

本文档说明如何配置导航栏中分类的显示顺序。

## 概述

导航栏中的分类顺序是通过数据库中 `DatabaseConfig` 表的 `sortOrder` 字段来控制的。数字越小的分类会显示在前面。

## 当前配置

目前的分类顺序配置：

1. **Marketed** (sortOrder: 100) - 已上市产品数据
2. **Regular** (sortOrder: 200) - 常规数据

## 管理命令

我们提供了一个分类顺序管理器来方便地调整分类顺序：

### 查看当前顺序

```bash
npm run category-order show
```

### 设置默认顺序

```bash
npm run category-order set
```

### 交换两个分类的顺序

```bash
npm run category-order swap Marketed Regular
```

### 快捷命令：让 Marketed 排在 Regular 前面

```bash
npm run category-order marketed-first
```

## 技术实现

### 1. 数据库配置

分类顺序通过 `DatabaseConfig` 表的 `sortOrder` 字段控制：

```sql
SELECT code, name, category, "sortOrder" 
FROM "DatabaseConfig" 
WHERE "isActive" = true 
ORDER BY "sortOrder";
```

### 2. API 接口

`/api/config/databases` 接口返回包含 `sortOrder` 信息的配置数据：

```typescript
{
  "success": true,
  "data": {
    "us_pmn": {
      "name": "US Premarket Notification",
      "category": "Marketed",
      "sortOrder": 100,
      // ...
    },
    "us_class": {
      "name": "US Classfication", 
      "category": "Regular",
      "sortOrder": 200,
      // ...
    }
  }
}
```

### 3. 前端组件

`Navigation.tsx` 组件会：

1. 获取数据库配置数据
2. 按分类分组
3. 根据每个分类的最小 `sortOrder` 值排序
4. 动态生成导航菜单

关键代码片段：

```typescript
// 创建分类到最小 sortOrder 的映射
const categoryToMinSortOrder = new Map<string, number>();
Object.values(databaseConfigs).forEach((config: any) => {
  const currentMin = categoryToMinSortOrder.get(config.category) || Infinity;
  categoryToMinSortOrder.set(config.category, Math.min(currentMin, config.sortOrder || 0));
});

// 按 sortOrder 排序分类
const sortedCategories = Array.from(new Set(Object.values(databaseConfigs).map((config: any) => config.category)))
  .sort((a, b) => {
    const sortOrderA = categoryToMinSortOrder.get(a) || 0;
    const sortOrderB = categoryToMinSortOrder.get(b) || 0;
    return sortOrderA - sortOrderB;
  });
```

## 添加新分类

如果需要添加新的分类，请：

1. 在数据库中添加新的 `DatabaseConfig` 记录
2. 设置合适的 `sortOrder` 值
3. 如果需要，更新 `scripts/category-order-manager.ts` 中的 `DEFAULT_CATEGORY_ORDER` 配置

## 默认分类顺序

```typescript
const DEFAULT_CATEGORY_ORDER = {
  'Marketed': 100,     // 已上市产品数据
  'Regular': 200,      // 常规数据
  '参考数据': 300,      // 参考数据
  '全球器械': 400,      // 全球器械数据
  'Regulation': 500,   // 监管数据
  '药物研发': 600,      // 药物研发数据
};
```

## 注意事项

1. **缓存清理**：修改分类顺序后，API 有缓存机制，可能需要等待缓存过期或重启服务器
2. **数据一致性**：确保同一分类下的所有数据库配置使用相同的 `sortOrder` 值
3. **备份**：在进行大量修改前，建议备份数据库配置

## 故障排除

### 分类顺序没有生效

1. 检查数据库中的 `sortOrder` 值是否正确设置
2. 清除浏览器缓存
3. 重启开发服务器
4. 检查 API 返回的数据是否包含正确的 `sortOrder` 信息

### 验证配置

使用测试脚本验证 API 返回的数据：

```bash
npx tsx scripts/test-api-order.ts
```

这个脚本会显示当前的分类顺序并验证 Marketed 是否排在 Regular 前面。
